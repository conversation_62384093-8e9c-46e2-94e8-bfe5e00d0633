// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"

/**
 * 
 */
class UNREALMCP_API UnrealMCPPythonExecutorCommands
{
public:
	UnrealMCPPythonExecutorCommands();
	~UnrealMCPPythonExecutorCommands();

	TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);

private:
	TSharedPtr<FJsonObject> HandleExecutePythonCode(const TSharedPtr<FJsonObject>& Params);

	FString ExecutePythonCode(const FString& Code);

};
