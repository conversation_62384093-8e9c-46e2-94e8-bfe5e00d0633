// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Commands/UnrealMCPBlueprintActionCommands.h"

#ifdef UNREALMCP_UnrealMCPBlueprintActionCommands_generated_h
#error "UnrealMCPBlueprintActionCommands.generated.h already included, missing '#pragma once' in UnrealMCPBlueprintActionCommands.h"
#endif
#define UNREALMCP_UnrealMCPBlueprintActionCommands_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UUnrealMCPBlueprintActionCommands ****************************************
#define FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPBlueprintActionCommands_h_14_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCreateNodeByActionName); \
	DECLARE_FUNCTION(execGetNodePinInfo); \
	DECLARE_FUNCTION(execSearchBlueprintActions); \
	DECLARE_FUNCTION(execGetActionsForClassHierarchy); \
	DECLARE_FUNCTION(execGetActionsForClass); \
	DECLARE_FUNCTION(execGetActionsForPin);


UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPBlueprintActionCommands_NoRegister();

#define FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPBlueprintActionCommands_h_14_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUUnrealMCPBlueprintActionCommands(); \
	friend struct Z_Construct_UClass_UUnrealMCPBlueprintActionCommands_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPBlueprintActionCommands_NoRegister(); \
public: \
	DECLARE_CLASS2(UUnrealMCPBlueprintActionCommands, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/UnrealMCP"), Z_Construct_UClass_UUnrealMCPBlueprintActionCommands_NoRegister) \
	DECLARE_SERIALIZER(UUnrealMCPBlueprintActionCommands)


#define FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPBlueprintActionCommands_h_14_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UUnrealMCPBlueprintActionCommands(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UUnrealMCPBlueprintActionCommands(UUnrealMCPBlueprintActionCommands&&) = delete; \
	UUnrealMCPBlueprintActionCommands(const UUnrealMCPBlueprintActionCommands&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UUnrealMCPBlueprintActionCommands); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UUnrealMCPBlueprintActionCommands); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UUnrealMCPBlueprintActionCommands) \
	NO_API virtual ~UUnrealMCPBlueprintActionCommands();


#define FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPBlueprintActionCommands_h_11_PROLOG
#define FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPBlueprintActionCommands_h_14_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPBlueprintActionCommands_h_14_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPBlueprintActionCommands_h_14_INCLASS_NO_PURE_DECLS \
	FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPBlueprintActionCommands_h_14_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UUnrealMCPBlueprintActionCommands;

// ********** End Class UUnrealMCPBlueprintActionCommands ******************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPBlueprintActionCommands_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
