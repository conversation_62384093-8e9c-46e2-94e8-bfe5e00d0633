// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for UnrealMCP
#pragma once
#include "SharedDefinitions.UnrealEd.Project.ValApi.ValExpApi.Cpp20.h"
#undef UNREALMCP_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_VALIDATE_EXPERIMENTAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 0
#define UE_PROJECT_NAME unrealmcp_test1
#define UE_TARGET_NAME unrealmcp_test1Editor
#define UE_MODULE_NAME "UnrealMCP"
#define UE_PLUGIN_NAME "UnrealMCP"
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define EDITORSCRIPTINGUTILITIES_API DLLIMPORT
#define STATICMESHEDITOR_API DLLIMPORT
#define ADVANCEDWIDGETS_API DLLIMPORT
#define UMGEDITOR_API DLLIMPORT
#define SEQUENCERCORE_API DLLIMPORT
#define CURVEEDITOR_API DLLIMPORT
#define SEQUENCERWIDGETS_API DLLIMPORT
#define SEQUENCER_API DLLIMPORT
#define SCENEOUTLINER_API DLLIMPORT
#define ENHANCEDINPUT_API DLLIMPORT
#define EDITORSTYLE_API DLLIMPORT
#define BLUEPRINTEDITORLIBRARY_API DLLIMPORT
#define UNREALMCP_API DLLEXPORT
