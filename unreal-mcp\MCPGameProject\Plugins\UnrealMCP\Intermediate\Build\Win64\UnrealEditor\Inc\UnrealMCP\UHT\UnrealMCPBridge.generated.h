// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "UnrealMCPBridge.h"

#ifdef UNREALMCP_UnrealMCPBridge_generated_h
#error "UnrealMCPBridge.generated.h already included, missing '#pragma once' in UnrealMCPBridge.h"
#endif
#define UNREALMCP_UnrealMCPBridge_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin Class UUnrealMCPBridge *********************************************************
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPBridge_NoRegister();

#define FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_UnrealMCPBridge_h_32_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUUnrealMCPBridge(); \
	friend struct Z_Construct_UClass_UUnrealMCPBridge_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPBridge_NoRegister(); \
public: \
	DECLARE_CLASS2(UUnrealMCPBridge, UEditorSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/UnrealMCP"), Z_Construct_UClass_UUnrealMCPBridge_NoRegister) \
	DECLARE_SERIALIZER(UUnrealMCPBridge)


#define FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_UnrealMCPBridge_h_32_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UUnrealMCPBridge(UUnrealMCPBridge&&) = delete; \
	UUnrealMCPBridge(const UUnrealMCPBridge&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UUnrealMCPBridge); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UUnrealMCPBridge); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UUnrealMCPBridge)


#define FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_UnrealMCPBridge_h_29_PROLOG
#define FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_UnrealMCPBridge_h_32_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_UnrealMCPBridge_h_32_INCLASS_NO_PURE_DECLS \
	FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_UnrealMCPBridge_h_32_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UUnrealMCPBridge;

// ********** End Class UUnrealMCPBridge ***********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_UnrealMCPBridge_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
