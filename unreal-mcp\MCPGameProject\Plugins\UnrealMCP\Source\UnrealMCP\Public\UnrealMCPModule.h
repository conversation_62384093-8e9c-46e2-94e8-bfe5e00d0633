#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"
#include "Interfaces/IHttpRequest.h"
#include "Interfaces/IHttpResponse.h"

class FUnrealMCPModule : public IModuleInterface
{
public:
	/** IModuleInterface implementation */
	virtual void StartupModule() override;
	virtual void ShutdownModule() override;

	static inline FUnrealMCPModule& Get()
	{
		return FModuleManager::LoadModuleChecked<FUnrealMCPModule>("UnrealMCP");
	}

	static inline bool IsAvailable()
	{
		return FModuleManager::Get().IsModuleLoaded("UnrealMCP");
	}

private:
	void ExtendLevelEditorToolbar();

	// MCP Control Panel functions
	void OpenMCPControlPanel();
	FReply OpenMCPControlPanel_OnClicked();
	void CloseMCPControlPanel();
	void OnMCPControlPanelClosed(const TSharedRef<SWindow>& Window);
	TSharedRef<class SWidget> CreateMCPControlPanelContent();

	// Chatbot functions
	void OnChatInputCommitted(const FText& Text, ETextCommit::Type CommitType);
	FReply OnSendMessageClicked();
	void AddMessageToChat(const FString& Message, bool bIsUserMessage = true);
	void ProcessUserMessage(const FString& Message);
	void SendMessageToAgent(const FString& Message);
	void CreateSessionThenSendMessage(const FString& Message);
	void OnSessionCreated(TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> Request, TSharedPtr<IHttpResponse, ESPMode::ThreadSafe> Response, bool bWasSuccessful, FString PendingMessage);
	void OnAgentResponseReceived(TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> Request, TSharedPtr<IHttpResponse, ESPMode::ThreadSafe> Response, bool bWasSuccessful);

	TSharedPtr<SWindow> MCPControlPanelWindow;
	TSharedPtr<class SScrollBox> ChatMessagesScrollBox;
	TSharedPtr<class SEditableTextBox> ChatInputTextBox;
	
	// Agent configuration
	FString AgentServerURL = TEXT("http://127.0.0.1:8000");  // ADK server URL
	FString CurrentSessionId;  // Track current session ID
	bool bSessionCreated = false;  // Track if session exists
};