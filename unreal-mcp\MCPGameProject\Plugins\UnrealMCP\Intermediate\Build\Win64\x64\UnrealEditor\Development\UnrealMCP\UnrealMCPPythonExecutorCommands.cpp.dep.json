{"Version": "1.2", "Data": {"Source": "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\private\\commands\\unrealmcppythonexecutorcommands.cpp", "ProvidedModule": "", "PCH": "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\intermediate\\build\\win64\\x64\\unrealmcp_test1editor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\definitions.unrealmcp.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcppythonexecutorcommands.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpcommonutils.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\json\\public\\json.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\core.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformnamedpipe.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\platformnamedpipe.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformnamedpipe.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformmutex.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\platformincludes.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\scopeddebuginfo.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\externalprofiler.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\stringutility.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\nameasstringproxyarchive.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\mruarray.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\arraybuilder.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\singlethreadevent.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadmanager.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\staticbitarray.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\mapbuilder.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadingbase.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\textlocalizationmanagerglobals.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\culture.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\logging\\logsuppressioninterface.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\outputdevices.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\logging\\logscopedverbosityoverride.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicenull.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicememory.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicefile.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicedebug.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicearchivewrapper.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceansierror.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\timeguard.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememorywriter.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememorydata.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememoryreader.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\bufferarchive.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\arrayreader.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\arraywriter.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\bufferwriter.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\wildcardstring.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\circularqueue.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicehelper.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\callbackdevice.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\localtimestampdirectoryvisitor.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\blueprintsobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\buildobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\coreobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\frameworkobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\mobileobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\networkingobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\onlineobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\platformobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\sequencerobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\vrobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceconsole.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monitoredprocess.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\plugins\\experimental\\pythonscriptplugin\\source\\pythonscriptplugin\\public\\ipythonscriptplugin.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\plugins\\experimental\\pythonscriptplugin\\source\\pythonscriptplugin\\public\\pythonscripttypes.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\plugins\\experimental\\pythonscriptplugin\\intermediate\\build\\win64\\unrealeditor\\inc\\pythonscriptplugin\\uht\\pythonscripttypes.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}