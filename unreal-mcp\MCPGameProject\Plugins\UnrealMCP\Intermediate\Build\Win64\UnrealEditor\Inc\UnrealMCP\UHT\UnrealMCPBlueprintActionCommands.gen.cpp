// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Commands/UnrealMCPBlueprintActionCommands.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeUnrealMCPBlueprintActionCommands() {}

// ********** Begin Cross Module References ********************************************************
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPBlueprintActionCommands();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPBlueprintActionCommands_NoRegister();
UPackage* Z_Construct_UPackage__Script_UnrealMCP();
// ********** End Cross Module References **********************************************************

// ********** Begin Class UUnrealMCPBlueprintActionCommands Function CreateNodeByActionName ********
struct Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics
{
	struct UnrealMCPBlueprintActionCommands_eventCreateNodeByActionName_Parms
	{
		FString BlueprintName;
		FString FunctionName;
		FString ClassName;
		FString NodePosition;
		FString JsonParams;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Unreal MCP Blueprint Actions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Create a blueprint node by discovered action/function name\n     * @param BlueprintName - Name of the target Blueprint\n     * @param FunctionName - Name of the function to create a node for (from discovered actions)\n     * @param ClassName - Optional class name if the function is from a specific class\n     * @param NodePosition - Optional [X, Y] position for the node\n     * @return JSON string containing node creation result\n     */" },
#endif
		{ "CPP_Default_ClassName", "" },
		{ "CPP_Default_JsonParams", "" },
		{ "CPP_Default_NodePosition", "" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPBlueprintActionCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Create a blueprint node by discovered action/function name\n@param BlueprintName - Name of the target Blueprint\n@param FunctionName - Name of the function to create a node for (from discovered actions)\n@param ClassName - Optional class name if the function is from a specific class\n@param NodePosition - Optional [X, Y] position for the node\n@return JSON string containing node creation result" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlueprintName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FunctionName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClassName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodePosition_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JsonParams_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BlueprintName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_FunctionName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ClassName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_NodePosition;
	static const UECodeGen_Private::FStrPropertyParams NewProp_JsonParams;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::NewProp_BlueprintName = { "BlueprintName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventCreateNodeByActionName_Parms, BlueprintName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlueprintName_MetaData), NewProp_BlueprintName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::NewProp_FunctionName = { "FunctionName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventCreateNodeByActionName_Parms, FunctionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FunctionName_MetaData), NewProp_FunctionName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::NewProp_ClassName = { "ClassName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventCreateNodeByActionName_Parms, ClassName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClassName_MetaData), NewProp_ClassName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::NewProp_NodePosition = { "NodePosition", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventCreateNodeByActionName_Parms, NodePosition), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodePosition_MetaData), NewProp_NodePosition_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::NewProp_JsonParams = { "JsonParams", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventCreateNodeByActionName_Parms, JsonParams), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JsonParams_MetaData), NewProp_JsonParams_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventCreateNodeByActionName_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::NewProp_BlueprintName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::NewProp_FunctionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::NewProp_ClassName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::NewProp_NodePosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::NewProp_JsonParams,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UUnrealMCPBlueprintActionCommands, nullptr, "CreateNodeByActionName", Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::PropPointers), sizeof(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::UnrealMCPBlueprintActionCommands_eventCreateNodeByActionName_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::Function_MetaDataParams), Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::UnrealMCPBlueprintActionCommands_eventCreateNodeByActionName_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UUnrealMCPBlueprintActionCommands::execCreateNodeByActionName)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BlueprintName);
	P_GET_PROPERTY(FStrProperty,Z_Param_FunctionName);
	P_GET_PROPERTY(FStrProperty,Z_Param_ClassName);
	P_GET_PROPERTY(FStrProperty,Z_Param_NodePosition);
	P_GET_PROPERTY(FStrProperty,Z_Param_JsonParams);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UUnrealMCPBlueprintActionCommands::CreateNodeByActionName(Z_Param_BlueprintName,Z_Param_FunctionName,Z_Param_ClassName,Z_Param_NodePosition,Z_Param_JsonParams);
	P_NATIVE_END;
}
// ********** End Class UUnrealMCPBlueprintActionCommands Function CreateNodeByActionName **********

// ********** Begin Class UUnrealMCPBlueprintActionCommands Function GetActionsForClass ************
struct Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics
{
	struct UnrealMCPBlueprintActionCommands_eventGetActionsForClass_Parms
	{
		FString ClassName;
		FString SearchFilter;
		int32 MaxResults;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Unreal MCP Blueprint Actions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get all available actions for a specific class with search filtering\n     * @param ClassName - Name or path of the class to get actions for\n     * @param SearchFilter - Optional search string to filter results (searches in name, keywords, category)\n     * @param MaxResults - Maximum number of results to return (default: 50)\n     * @return JSON string containing available actions\n     */" },
#endif
		{ "CPP_Default_MaxResults", "50" },
		{ "CPP_Default_SearchFilter", "" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPBlueprintActionCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get all available actions for a specific class with search filtering\n@param ClassName - Name or path of the class to get actions for\n@param SearchFilter - Optional search string to filter results (searches in name, keywords, category)\n@param MaxResults - Maximum number of results to return (default: 50)\n@return JSON string containing available actions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClassName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SearchFilter_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ClassName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SearchFilter;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxResults;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics::NewProp_ClassName = { "ClassName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventGetActionsForClass_Parms, ClassName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClassName_MetaData), NewProp_ClassName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics::NewProp_SearchFilter = { "SearchFilter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventGetActionsForClass_Parms, SearchFilter), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SearchFilter_MetaData), NewProp_SearchFilter_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics::NewProp_MaxResults = { "MaxResults", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventGetActionsForClass_Parms, MaxResults), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventGetActionsForClass_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics::NewProp_ClassName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics::NewProp_SearchFilter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics::NewProp_MaxResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UUnrealMCPBlueprintActionCommands, nullptr, "GetActionsForClass", Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics::PropPointers), sizeof(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics::UnrealMCPBlueprintActionCommands_eventGetActionsForClass_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics::Function_MetaDataParams), Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics::UnrealMCPBlueprintActionCommands_eventGetActionsForClass_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UUnrealMCPBlueprintActionCommands::execGetActionsForClass)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ClassName);
	P_GET_PROPERTY(FStrProperty,Z_Param_SearchFilter);
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxResults);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UUnrealMCPBlueprintActionCommands::GetActionsForClass(Z_Param_ClassName,Z_Param_SearchFilter,Z_Param_MaxResults);
	P_NATIVE_END;
}
// ********** End Class UUnrealMCPBlueprintActionCommands Function GetActionsForClass **************

// ********** Begin Class UUnrealMCPBlueprintActionCommands Function GetActionsForClassHierarchy ***
struct Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics
{
	struct UnrealMCPBlueprintActionCommands_eventGetActionsForClassHierarchy_Parms
	{
		FString ClassName;
		FString SearchFilter;
		int32 MaxResults;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Unreal MCP Blueprint Actions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get all available actions for a class and its entire inheritance hierarchy with search filtering\n     * @param ClassName - Name or path of the class to get actions for\n     * @param SearchFilter - Optional search string to filter results (searches in name, keywords, category)\n     * @param MaxResults - Maximum number of results to return (default: 50)\n     * @return JSON string containing available actions and hierarchy info\n     */" },
#endif
		{ "CPP_Default_MaxResults", "50" },
		{ "CPP_Default_SearchFilter", "" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPBlueprintActionCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get all available actions for a class and its entire inheritance hierarchy with search filtering\n@param ClassName - Name or path of the class to get actions for\n@param SearchFilter - Optional search string to filter results (searches in name, keywords, category)\n@param MaxResults - Maximum number of results to return (default: 50)\n@return JSON string containing available actions and hierarchy info" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ClassName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SearchFilter_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ClassName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SearchFilter;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxResults;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics::NewProp_ClassName = { "ClassName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventGetActionsForClassHierarchy_Parms, ClassName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ClassName_MetaData), NewProp_ClassName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics::NewProp_SearchFilter = { "SearchFilter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventGetActionsForClassHierarchy_Parms, SearchFilter), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SearchFilter_MetaData), NewProp_SearchFilter_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics::NewProp_MaxResults = { "MaxResults", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventGetActionsForClassHierarchy_Parms, MaxResults), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventGetActionsForClassHierarchy_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics::NewProp_ClassName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics::NewProp_SearchFilter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics::NewProp_MaxResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UUnrealMCPBlueprintActionCommands, nullptr, "GetActionsForClassHierarchy", Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics::PropPointers), sizeof(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics::UnrealMCPBlueprintActionCommands_eventGetActionsForClassHierarchy_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics::Function_MetaDataParams), Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics::UnrealMCPBlueprintActionCommands_eventGetActionsForClassHierarchy_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UUnrealMCPBlueprintActionCommands::execGetActionsForClassHierarchy)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ClassName);
	P_GET_PROPERTY(FStrProperty,Z_Param_SearchFilter);
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxResults);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UUnrealMCPBlueprintActionCommands::GetActionsForClassHierarchy(Z_Param_ClassName,Z_Param_SearchFilter,Z_Param_MaxResults);
	P_NATIVE_END;
}
// ********** End Class UUnrealMCPBlueprintActionCommands Function GetActionsForClassHierarchy *****

// ********** Begin Class UUnrealMCPBlueprintActionCommands Function GetActionsForPin **************
struct Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics
{
	struct UnrealMCPBlueprintActionCommands_eventGetActionsForPin_Parms
	{
		FString PinType;
		FString PinSubCategory;
		FString SearchFilter;
		int32 MaxResults;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Unreal MCP Blueprint Actions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get all available actions for a specific pin type with search filtering\n     * @param PinType - The type of pin (object, int, float, bool, string, struct, etc.)\n     * @param PinSubCategory - The subcategory/class name for object pins\n     * @param SearchFilter - Optional search string to filter results (searches in name, keywords, category)\n     * @param MaxResults - Maximum number of results to return (default: 50)\n     * @return JSON string containing available actions\n     */" },
#endif
		{ "CPP_Default_MaxResults", "50" },
		{ "CPP_Default_SearchFilter", "" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPBlueprintActionCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get all available actions for a specific pin type with search filtering\n@param PinType - The type of pin (object, int, float, bool, string, struct, etc.)\n@param PinSubCategory - The subcategory/class name for object pins\n@param SearchFilter - Optional search string to filter results (searches in name, keywords, category)\n@param MaxResults - Maximum number of results to return (default: 50)\n@return JSON string containing available actions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PinType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PinSubCategory_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SearchFilter_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PinType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PinSubCategory;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SearchFilter;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxResults;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::NewProp_PinType = { "PinType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventGetActionsForPin_Parms, PinType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PinType_MetaData), NewProp_PinType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::NewProp_PinSubCategory = { "PinSubCategory", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventGetActionsForPin_Parms, PinSubCategory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PinSubCategory_MetaData), NewProp_PinSubCategory_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::NewProp_SearchFilter = { "SearchFilter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventGetActionsForPin_Parms, SearchFilter), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SearchFilter_MetaData), NewProp_SearchFilter_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::NewProp_MaxResults = { "MaxResults", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventGetActionsForPin_Parms, MaxResults), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventGetActionsForPin_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::NewProp_PinType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::NewProp_PinSubCategory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::NewProp_SearchFilter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::NewProp_MaxResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UUnrealMCPBlueprintActionCommands, nullptr, "GetActionsForPin", Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::PropPointers), sizeof(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::UnrealMCPBlueprintActionCommands_eventGetActionsForPin_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::Function_MetaDataParams), Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::UnrealMCPBlueprintActionCommands_eventGetActionsForPin_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UUnrealMCPBlueprintActionCommands::execGetActionsForPin)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_PinType);
	P_GET_PROPERTY(FStrProperty,Z_Param_PinSubCategory);
	P_GET_PROPERTY(FStrProperty,Z_Param_SearchFilter);
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxResults);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UUnrealMCPBlueprintActionCommands::GetActionsForPin(Z_Param_PinType,Z_Param_PinSubCategory,Z_Param_SearchFilter,Z_Param_MaxResults);
	P_NATIVE_END;
}
// ********** End Class UUnrealMCPBlueprintActionCommands Function GetActionsForPin ****************

// ********** Begin Class UUnrealMCPBlueprintActionCommands Function GetNodePinInfo ****************
struct Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo_Statics
{
	struct UnrealMCPBlueprintActionCommands_eventGetNodePinInfo_Parms
	{
		FString NodeName;
		FString PinName;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Unreal MCP Blueprint Actions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Get specific node pin information\n     * @param NodeName - Name of the node\n     * @param PinName - Name of the pin\n     * @return JSON string containing pin information\n     */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPBlueprintActionCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get specific node pin information\n@param NodeName - Name of the node\n@param PinName - Name of the pin\n@return JSON string containing pin information" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NodeName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PinName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_NodeName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PinName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo_Statics::NewProp_NodeName = { "NodeName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventGetNodePinInfo_Parms, NodeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NodeName_MetaData), NewProp_NodeName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo_Statics::NewProp_PinName = { "PinName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventGetNodePinInfo_Parms, PinName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PinName_MetaData), NewProp_PinName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventGetNodePinInfo_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo_Statics::NewProp_NodeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo_Statics::NewProp_PinName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UUnrealMCPBlueprintActionCommands, nullptr, "GetNodePinInfo", Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo_Statics::UnrealMCPBlueprintActionCommands_eventGetNodePinInfo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo_Statics::UnrealMCPBlueprintActionCommands_eventGetNodePinInfo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UUnrealMCPBlueprintActionCommands::execGetNodePinInfo)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_NodeName);
	P_GET_PROPERTY(FStrProperty,Z_Param_PinName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UUnrealMCPBlueprintActionCommands::GetNodePinInfo(Z_Param_NodeName,Z_Param_PinName);
	P_NATIVE_END;
}
// ********** End Class UUnrealMCPBlueprintActionCommands Function GetNodePinInfo ******************

// ********** Begin Class UUnrealMCPBlueprintActionCommands Function SearchBlueprintActions ********
struct Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics
{
	struct UnrealMCPBlueprintActionCommands_eventSearchBlueprintActions_Parms
	{
		FString SearchQuery;
		FString Category;
		int32 MaxResults;
		FString BlueprintName;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Unreal MCP Blueprint Actions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n     * Search for Blueprint actions using keywords\n     * @param SearchQuery - Search string to find actions (searches in name, keywords, category, tooltip)\n     * @param Category - Optional category filter (Flow Control, Math, Utilities, etc.)\n     * @param MaxResults - Maximum number of results to return (default: 50)\n     * @param BlueprintName - Optional name of the Blueprint asset for local variable discovery\n     * @return JSON string containing matching actions\n     */" },
#endif
		{ "CPP_Default_BlueprintName", "" },
		{ "CPP_Default_Category", "" },
		{ "CPP_Default_MaxResults", "50" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPBlueprintActionCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Search for Blueprint actions using keywords\n@param SearchQuery - Search string to find actions (searches in name, keywords, category, tooltip)\n@param Category - Optional category filter (Flow Control, Math, Utilities, etc.)\n@param MaxResults - Maximum number of results to return (default: 50)\n@param BlueprintName - Optional name of the Blueprint asset for local variable discovery\n@return JSON string containing matching actions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SearchQuery_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlueprintName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SearchQuery;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Category;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxResults;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BlueprintName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::NewProp_SearchQuery = { "SearchQuery", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventSearchBlueprintActions_Parms, SearchQuery), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SearchQuery_MetaData), NewProp_SearchQuery_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventSearchBlueprintActions_Parms, Category), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::NewProp_MaxResults = { "MaxResults", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventSearchBlueprintActions_Parms, MaxResults), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::NewProp_BlueprintName = { "BlueprintName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventSearchBlueprintActions_Parms, BlueprintName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlueprintName_MetaData), NewProp_BlueprintName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UnrealMCPBlueprintActionCommands_eventSearchBlueprintActions_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::NewProp_SearchQuery,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::NewProp_MaxResults,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::NewProp_BlueprintName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UUnrealMCPBlueprintActionCommands, nullptr, "SearchBlueprintActions", Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::UnrealMCPBlueprintActionCommands_eventSearchBlueprintActions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::UnrealMCPBlueprintActionCommands_eventSearchBlueprintActions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UUnrealMCPBlueprintActionCommands::execSearchBlueprintActions)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SearchQuery);
	P_GET_PROPERTY(FStrProperty,Z_Param_Category);
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxResults);
	P_GET_PROPERTY(FStrProperty,Z_Param_BlueprintName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=UUnrealMCPBlueprintActionCommands::SearchBlueprintActions(Z_Param_SearchQuery,Z_Param_Category,Z_Param_MaxResults,Z_Param_BlueprintName);
	P_NATIVE_END;
}
// ********** End Class UUnrealMCPBlueprintActionCommands Function SearchBlueprintActions **********

// ********** Begin Class UUnrealMCPBlueprintActionCommands ****************************************
void UUnrealMCPBlueprintActionCommands::StaticRegisterNativesUUnrealMCPBlueprintActionCommands()
{
	UClass* Class = UUnrealMCPBlueprintActionCommands::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CreateNodeByActionName", &UUnrealMCPBlueprintActionCommands::execCreateNodeByActionName },
		{ "GetActionsForClass", &UUnrealMCPBlueprintActionCommands::execGetActionsForClass },
		{ "GetActionsForClassHierarchy", &UUnrealMCPBlueprintActionCommands::execGetActionsForClassHierarchy },
		{ "GetActionsForPin", &UUnrealMCPBlueprintActionCommands::execGetActionsForPin },
		{ "GetNodePinInfo", &UUnrealMCPBlueprintActionCommands::execGetNodePinInfo },
		{ "SearchBlueprintActions", &UUnrealMCPBlueprintActionCommands::execSearchBlueprintActions },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UUnrealMCPBlueprintActionCommands;
UClass* UUnrealMCPBlueprintActionCommands::GetPrivateStaticClass()
{
	using TClass = UUnrealMCPBlueprintActionCommands;
	if (!Z_Registration_Info_UClass_UUnrealMCPBlueprintActionCommands.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("UnrealMCPBlueprintActionCommands"),
			Z_Registration_Info_UClass_UUnrealMCPBlueprintActionCommands.InnerSingleton,
			StaticRegisterNativesUUnrealMCPBlueprintActionCommands,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UUnrealMCPBlueprintActionCommands.InnerSingleton;
}
UClass* Z_Construct_UClass_UUnrealMCPBlueprintActionCommands_NoRegister()
{
	return UUnrealMCPBlueprintActionCommands::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UUnrealMCPBlueprintActionCommands_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Commands for discovering Blueprint actions dynamically using the FBlueprintActionDatabase\n */" },
#endif
		{ "IncludePath", "Commands/UnrealMCPBlueprintActionCommands.h" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPBlueprintActionCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Commands for discovering Blueprint actions dynamically using the FBlueprintActionDatabase" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_CreateNodeByActionName, "CreateNodeByActionName" }, // 3469134818
		{ &Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClass, "GetActionsForClass" }, // 2117208324
		{ &Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForClassHierarchy, "GetActionsForClassHierarchy" }, // 2762367250
		{ &Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetActionsForPin, "GetActionsForPin" }, // 3978520487
		{ &Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_GetNodePinInfo, "GetNodePinInfo" }, // 71083703
		{ &Z_Construct_UFunction_UUnrealMCPBlueprintActionCommands_SearchBlueprintActions, "SearchBlueprintActions" }, // 828005937
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UUnrealMCPBlueprintActionCommands>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UUnrealMCPBlueprintActionCommands_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPBlueprintActionCommands_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UUnrealMCPBlueprintActionCommands_Statics::ClassParams = {
	&UUnrealMCPBlueprintActionCommands::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPBlueprintActionCommands_Statics::Class_MetaDataParams), Z_Construct_UClass_UUnrealMCPBlueprintActionCommands_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UUnrealMCPBlueprintActionCommands()
{
	if (!Z_Registration_Info_UClass_UUnrealMCPBlueprintActionCommands.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UUnrealMCPBlueprintActionCommands.OuterSingleton, Z_Construct_UClass_UUnrealMCPBlueprintActionCommands_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UUnrealMCPBlueprintActionCommands.OuterSingleton;
}
UUnrealMCPBlueprintActionCommands::UUnrealMCPBlueprintActionCommands(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UUnrealMCPBlueprintActionCommands);
UUnrealMCPBlueprintActionCommands::~UUnrealMCPBlueprintActionCommands() {}
// ********** End Class UUnrealMCPBlueprintActionCommands ******************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPBlueprintActionCommands_h__Script_UnrealMCP_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UUnrealMCPBlueprintActionCommands, UUnrealMCPBlueprintActionCommands::StaticClass, TEXT("UUnrealMCPBlueprintActionCommands"), &Z_Registration_Info_UClass_UUnrealMCPBlueprintActionCommands, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UUnrealMCPBlueprintActionCommands), 1857101482U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPBlueprintActionCommands_h__Script_UnrealMCP_2193920222(TEXT("/Script/UnrealMCP"),
	Z_CompiledInDeferFile_FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPBlueprintActionCommands_h__Script_UnrealMCP_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_UE5_projects_unrealmcp_test1_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPBlueprintActionCommands_h__Script_UnrealMCP_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
