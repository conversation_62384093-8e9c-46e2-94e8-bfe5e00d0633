{"Version": "1.2", "Data": {"Source": "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\private\\unrealmcpbridge.cpp", "ProvidedModule": "", "PCH": "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\intermediate\\build\\win64\\x64\\unrealmcp_test1editor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\definitions.unrealmcp.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\unrealmcpbridge.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\sockets\\public\\sockets.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\sockets\\public\\socketsubsystem.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\sockets\\public\\addressinfotypes.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\http.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\httpmodule.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\interfaces\\ihttprequest.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\interfaces\\ihttpbase.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\interfaces\\ihttpresponse.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\interfaces\\ihttpresponsecodes.inl", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\interfaces\\ihttpresponsecodes.inl", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\platformhttp.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\windows\\windowsplatformhttp.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\genericplatform\\genericplatformhttp.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\json\\public\\json.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\core.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformnamedpipe.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\platformnamedpipe.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformnamedpipe.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformmutex.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\platformincludes.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\scopeddebuginfo.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\externalprofiler.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\stringutility.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\nameasstringproxyarchive.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\mruarray.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\arraybuilder.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\singlethreadevent.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadmanager.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\staticbitarray.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\mapbuilder.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadingbase.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\textlocalizationmanagerglobals.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\culture.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\logging\\logsuppressioninterface.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\outputdevices.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\logging\\logscopedverbosityoverride.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicenull.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicememory.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicefile.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicedebug.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicearchivewrapper.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceansierror.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\timeguard.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememorywriter.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememorydata.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememoryreader.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\bufferarchive.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\arrayreader.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\arraywriter.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\bufferwriter.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\wildcardstring.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\circularqueue.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicehelper.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\callbackdevice.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\localtimestampdirectoryvisitor.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\blueprintsobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\buildobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\coreobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\frameworkobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\mobileobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\networkingobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\onlineobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\platformobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\sequencerobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\vrobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceconsole.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monitoredprocess.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\networking\\public\\interfaces\\ipv4\\ipv4address.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\networking\\public\\interfaces\\ipv4\\ipv4subnetmask.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\networking\\public\\interfaces\\ipv4\\ipv4endpoint.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpeditorcommands.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpblueprintcommands.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpblueprintnodecommands.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpprojectcommands.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpumgcommands.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpdatatablecommands.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpblueprintactioncommandshandler.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpbridge.generated.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\mcpserverrunnable.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\staticmeshactor.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\staticmeshactor.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\directionallight.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\light.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\light.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\directionallight.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\pointlight.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\pointlight.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\spotlight.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\spotlight.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\camera\\cameraactor.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\cameraactor.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\plugins\\editor\\editorscriptingutilities\\source\\editorscriptingutilities\\public\\editorassetlibrary.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\plugins\\editor\\editorscriptingutilities\\intermediate\\build\\win64\\unrealeditor\\inc\\editorscriptingutilities\\uht\\editorassetlibrary.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\assetregistrymodule.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\iassetregistry.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\misc\\assetregistryinterface.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\assetregistry\\uht\\iassetregistry.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\jsonutilities\\public\\jsonobjectconverter.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\jsonutilities\\public\\jsonobjectwrapper.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\jsonutilities\\uht\\jsonobjectwrapper.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\selection.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\selection.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\typedelementframework\\public\\elements\\framework\\typedelementlist.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\typedelementframework\\public\\elements\\framework\\typedelementcounter.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\typedelementframework\\uht\\typedelementcounter.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\typedelementruntime\\public\\elements\\framework\\typedelementselectionset.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\typedelementruntime\\public\\elements\\framework\\typedelementinterfacecustomization.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\typedelementruntime\\public\\elements\\framework\\typedelementlistobjectutil.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\typedelementruntime\\public\\elements\\interfaces\\typedelementselectioninterface.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\typedelementframework\\public\\elements\\framework\\typedelementlistproxy.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\typedelementframework\\uht\\typedelementlistproxy.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\typedelementruntime\\uht\\typedelementselectioninterface.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\typedelementruntime\\uht\\typedelementselectionset.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\selection.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\dialoguetypes.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystatics.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\unrealed\\classes\\factories\\blueprintfactory.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\blueprintfactory.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_event.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_eventnodeinterface.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_eventnodeinterface.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\kismetcompilermisc.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\bpterminal.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\blueprintcompiledstatement.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_event.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_variableget.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_variable.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_variable.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_variableget.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_variableset.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_variableset.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\boxcomponent.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\shapecomponent.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\shapecomponent.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\boxcomponent.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\spherecomponent.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\spherecomponent.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\kismet2\\blueprinteditorutils.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\classviewer\\public\\classviewermodule.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\kismet2\\kismeteditorutilities.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\simpleconstructionscript.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\simpleconstructionscript.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\scs_node.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\scs_node.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_callfunction.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_callfunction.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_inputaction.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_inputaction.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_self.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_self.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\inputsettings.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\playerinput.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\public\\gesturerecognizer.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\public\\keystate.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\playerinput.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\applicationcore\\public\\genericplatform\\inputdevicemappingpolicy.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\developersettings\\public\\engine\\platformsettings.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\developersettings\\public\\engine\\platformsettingsmanager.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\developersettings\\uht\\platformsettingsmanager.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\developersettings\\uht\\platformsettings.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\inputsettings.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\subsystems\\editoractorsubsystem.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\editoractorsubsystem.generated.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpcommonutils.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}