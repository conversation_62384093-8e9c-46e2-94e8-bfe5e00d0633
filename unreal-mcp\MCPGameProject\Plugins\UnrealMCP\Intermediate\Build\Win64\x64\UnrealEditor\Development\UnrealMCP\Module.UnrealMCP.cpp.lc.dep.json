{"Version": "1.2", "Data": {"Source": "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\module.unrealmcp.cpp", "ProvidedModule": "", "PCH": "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\intermediate\\build\\win64\\x64\\unrealmcp_test1editor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\definitions.unrealmcp.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcp.init.gen.cpp", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpblueprintactioncommands.gen.cpp", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpblueprintactioncommands.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\noexporttypes.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\serialization\\testundeclaredscriptstructobjectreferences.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.inl", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\polyglottextdata.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpblueprintactioncommands.generated.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpbridge.gen.cpp", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\unrealmcpbridge.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\sockets\\public\\sockets.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\sockets\\public\\socketsubsystem.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\sockets\\public\\addressinfotypes.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\http.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\httpmodule.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\interfaces\\ihttprequest.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\interfaces\\ihttpbase.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\interfaces\\ihttpresponse.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\interfaces\\ihttpresponsecodes.inl", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\interfaces\\ihttpresponsecodes.inl", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\platformhttp.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\windows\\windowsplatformhttp.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\online\\http\\public\\genericplatform\\genericplatformhttp.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\json\\public\\json.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\core.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformnamedpipe.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\platformnamedpipe.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformnamedpipe.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformmutex.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\platformincludes.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\scopeddebuginfo.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\externalprofiler.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\stringutility.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\nameasstringproxyarchive.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\mruarray.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\arraybuilder.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\singlethreadevent.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadmanager.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\staticbitarray.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\mapbuilder.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadingbase.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\textlocalizationmanagerglobals.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\culture.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\logging\\logsuppressioninterface.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\outputdevices.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\logging\\logscopedverbosityoverride.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicenull.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicememory.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicefile.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicedebug.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicearchivewrapper.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceansierror.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\timeguard.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememorywriter.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememorydata.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememoryreader.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\bufferarchive.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\arrayreader.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\arraywriter.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\bufferwriter.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\wildcardstring.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\circularqueue.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicehelper.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\callbackdevice.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\localtimestampdirectoryvisitor.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\blueprintsobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\buildobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\coreobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\frameworkobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\mobileobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\networkingobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\onlineobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\platformobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\sequencerobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\vrobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceconsole.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monitoredprocess.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\networking\\public\\interfaces\\ipv4\\ipv4address.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\networking\\public\\interfaces\\ipv4\\ipv4subnetmask.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\networking\\public\\interfaces\\ipv4\\ipv4endpoint.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpeditorcommands.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpblueprintcommands.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpblueprintnodecommands.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpprojectcommands.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpumgcommands.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpdatatablecommands.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpblueprintactioncommandshandler.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpbridge.generated.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\permoduleinline.gen.cpp", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\permoduleinline.inl"], "ImportedModules": [], "ImportedHeaderUnits": []}}