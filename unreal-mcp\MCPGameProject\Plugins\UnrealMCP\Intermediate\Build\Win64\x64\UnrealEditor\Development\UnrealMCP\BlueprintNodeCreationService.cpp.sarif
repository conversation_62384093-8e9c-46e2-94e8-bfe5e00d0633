{"version": "2.1.0", "$schema": "https://schemastore.azurewebsites.net/schemas/json/sarif-2.1.0-rtm.5.json", "runs": [{"results": [{"ruleId": "C4457", "message": {"text": "declaration of 'ClassName' hides function parameter"}, "analysisTarget": {"uri": "file:///E:/Epic Games/Unreal Engine/UE5 projects/unrealmcp_test1/Plugins/UnrealMCP/Source/UnrealMCP/Private/Services/BlueprintNodeCreationService.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///E:/Epic Games/Unreal Engine/UE5 projects/unrealmcp_test1/Plugins/UnrealMCP/Source/UnrealMCP/Private/Services/BlueprintNodeCreationService.cpp"}, "region": {"startLine": 271, "startColumn": 49, "snippet": {"text": "                                        FString ClassName = TestBP->GeneratedClass->GetName();"}}}}], "relatedLocations": [{"id": 0, "physicalLocation": {"artifactLocation": {"uri": "file:///E:/Epic Games/Unreal Engine/UE5 projects/unrealmcp_test1/Plugins/UnrealMCP/Source/UnrealMCP/Private/Services/BlueprintNodeCreationService.cpp"}, "region": {"startLine": 65, "startColumn": 137, "snippet": {"text": "FString FBlueprintNodeCreationService::CreateNodeByActionName(const FString& BlueprintName, const FString& FunctionName, const FString& ClassName, const FString& NodePosition, const FString& JsonParams)"}}}, "message": {"text": "see declaration of 'ClassName'"}}]}], "tool": {"driver": {"name": "MSVC", "shortDescription": {"text": "Microsoft Visual C++ Compiler Warnings/Errors"}, "informationUri": "https://docs.microsoft.com/cpp/error-messages/compiler-errors-1/c-cpp-build-errors"}}}]}