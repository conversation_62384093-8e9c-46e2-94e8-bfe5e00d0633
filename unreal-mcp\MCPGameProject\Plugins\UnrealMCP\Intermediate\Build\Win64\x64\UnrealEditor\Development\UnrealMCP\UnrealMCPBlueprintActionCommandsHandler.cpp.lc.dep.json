{"Version": "1.2", "Data": {"Source": "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\private\\commands\\unrealmcpblueprintactioncommandshandler.cpp", "ProvidedModule": "", "PCH": "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\intermediate\\build\\win64\\x64\\unrealmcp_test1editor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\definitions.unrealmcp.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpblueprintactioncommandshandler.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpblueprintactioncommands.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\noexporttypes.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\serialization\\testundeclaredscriptstructobjectreferences.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.inl", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\polyglottextdata.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpblueprintactioncommands.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}