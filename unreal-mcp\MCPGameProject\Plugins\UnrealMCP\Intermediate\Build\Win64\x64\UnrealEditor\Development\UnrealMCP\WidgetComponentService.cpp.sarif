{"version": "2.1.0", "$schema": "https://schemastore.azurewebsites.net/schemas/json/sarif-2.1.0-rtm.5.json", "runs": [{"results": [{"ruleId": "C4996", "message": {"text": "'UButton::WidgetStyle': Direct access to WidgetStyle is deprecated. Please use the getter and setter. - Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile."}, "analysisTarget": {"uri": "file:///E:/Epic Games/Unreal Engine/UE5 projects/unrealmcp_test1/Plugins/UnrealMCP/Source/UnrealMCP/Private/Services/UMG/WidgetComponentService.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///E:/Epic Games/Unreal Engine/UE5 projects/unrealmcp_test1/Plugins/UnrealMCP/Source/UnrealMCP/Private/Services/UMG/WidgetComponentService.cpp"}, "region": {"startLine": 399, "startColumn": 15, "snippet": {"text": "        Button->WidgetStyle.Normal.TintColor = BackgroundColor;"}}}}]}, {"ruleId": "C4996", "message": {"text": "'UButton::WidgetStyle': Direct access to WidgetStyle is deprecated. Please use the getter and setter. - Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile."}, "analysisTarget": {"uri": "file:///E:/Epic Games/Unreal Engine/UE5 projects/unrealmcp_test1/Plugins/UnrealMCP/Source/UnrealMCP/Private/Services/UMG/WidgetComponentService.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///E:/Epic Games/Unreal Engine/UE5 projects/unrealmcp_test1/Plugins/UnrealMCP/Source/UnrealMCP/Private/Services/UMG/WidgetComponentService.cpp"}, "region": {"startLine": 400, "startColumn": 15, "snippet": {"text": "        Button->WidgetStyle.Hovered.TintColor = BackgroundColor;"}}}}]}, {"ruleId": "C4996", "message": {"text": "'UButton::WidgetStyle': Direct access to WidgetStyle is deprecated. Please use the getter and setter. - Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile."}, "analysisTarget": {"uri": "file:///E:/Epic Games/Unreal Engine/UE5 projects/unrealmcp_test1/Plugins/UnrealMCP/Source/UnrealMCP/Private/Services/UMG/WidgetComponentService.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///E:/Epic Games/Unreal Engine/UE5 projects/unrealmcp_test1/Plugins/UnrealMCP/Source/UnrealMCP/Private/Services/UMG/WidgetComponentService.cpp"}, "region": {"startLine": 401, "startColumn": 15, "snippet": {"text": "        Button->WidgetStyle.Pressed.TintColor = BackgroundColor;"}}}}]}], "tool": {"driver": {"name": "MSVC", "shortDescription": {"text": "Microsoft Visual C++ Compiler Warnings/Errors"}, "informationUri": "https://docs.microsoft.com/cpp/error-messages/compiler-errors-1/c-cpp-build-errors"}}}]}