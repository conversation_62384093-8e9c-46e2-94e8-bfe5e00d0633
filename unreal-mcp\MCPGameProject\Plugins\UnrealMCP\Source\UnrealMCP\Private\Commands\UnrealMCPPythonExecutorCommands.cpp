// Fill out your copyright notice in the Description page of Project Settings.


#include "Commands/UnrealMCPPythonExecutorCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"
#include "IPythonScriptPlugin.h"
#include "Engine/Engine.h"

UnrealMCPPythonExecutorCommands::UnrealMCPPythonExecutorCommands()
{
}

UnrealMCPPythonExecutorCommands::~UnrealMCPPythonExecutorCommands()
{
}


TSharedPtr<FJsonObject> UnrealMCPPythonExecutorCommands::HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params)
{
	if (CommandName == TEXT("execute_python_code"))
	{
		return HandleExecutePythonCode(Params);
	}

	return FUnrealMCPCommonUtils::CreateErrorResponse(*FString::Printf(TEXT("Unknown UMG command: %s"), *CommandName));
}


TSharedPtr<FJsonObject> UnrealMCPPythonExecutorCommands::HandleExecutePythonCode(const TSharedPtr<FJsonObject>& Params)
{
	UE_LOG(LogTemp, Log, TEXT("Received Python code execute request"));

	TSharedPtr<FJsonObject> ResponseObject = MakeShared<FJsonObject>();

	// Get the Python code from the request
	FString Code;
	if (!Params->TryGetStringField(TEXT("code"), Code))
	{
		// Code parameter is missing
		ResponseObject->SetStringField("status", "error");
		ResponseObject->SetStringField("message", "Missing 'code' parameter");

		return ResponseObject;
	}

	UE_LOG(LogTemp, Log, TEXT("Executing Python Code: %s"), *Code);
	// Execute the Python code
	FString Result = ExecutePythonCode(Code);

	ResponseObject->SetBoolField(TEXT("success"), true);
	ResponseObject->SetStringField("result", Result);

	return ResponseObject;
}

FString UnrealMCPPythonExecutorCommands::ExecutePythonCode(const FString& Code)
{
	
	if (IPythonScriptPlugin* Py = IPythonScriptPlugin::Get())
	{
		FPythonCommandEx PythonCommand;
		PythonCommand.Command = *Code;
		Py->ExecPythonCommandEx(PythonCommand);
		FString Output;
		Output = "Command Result:\n";
		Output += PythonCommand.CommandResult;
		Output += "\nLog Output:\n";
		for (int i = 0; i < PythonCommand.LogOutput.Num(); i++)
		{ 
			Output += PythonCommand.LogOutput[i].Output + '\n';
		}

		return Output;
	}
	
	// Fallback via console command (requires Python plugin to register 'py' command)
	if (GEngine)
	{
		const FString Cmd = FString::Printf(TEXT("py %s"), *Code);
		FStringOutputDevice OutputDevice;
		OutputDevice.SetAutoEmitLineTerminator(true);
		// This method is not the best, as it can lead to including logs from other commands run in this same time
		// TODO: fix this issue.
		FOutputDeviceRedirector::Get()->AddOutputDevice(&OutputDevice);
		const bool bOk = GEngine->Exec(nullptr, *Cmd);
		FOutputDeviceRedirector::Get()->RemoveOutputDevice(&OutputDevice);
		FString Output;
		Output = "Command Result:\n";
		Output += bOk ? "Executed" : "Execution failed";
		Output += "\nOutput:\n";
		Output += OutputDevice;
		return Output;
	}

	return TEXT("Engine not available");
}