{"Version": "1.2", "Data": {"Source": "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\private\\commands\\unrealmcpeditorcommands.cpp", "ProvidedModule": "", "PCH": "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\intermediate\\build\\win64\\x64\\unrealmcp_test1editor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\definitions.unrealmcp.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpeditorcommands.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\json\\public\\json.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\core.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformnamedpipe.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\platformnamedpipe.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformnamedpipe.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformmutex.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\platformincludes.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\scopeddebuginfo.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\externalprofiler.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\stringutility.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\nameasstringproxyarchive.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\mruarray.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\arraybuilder.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\singlethreadevent.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadmanager.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\staticbitarray.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\mapbuilder.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadingbase.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\textlocalizationmanagerglobals.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\culture.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\logging\\logsuppressioninterface.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\outputdevices.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\logging\\logscopedverbosityoverride.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicenull.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicememory.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicefile.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicedebug.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicearchivewrapper.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceansierror.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\timeguard.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememorywriter.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememorydata.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememoryreader.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\bufferarchive.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\arrayreader.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\arraywriter.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\bufferwriter.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\wildcardstring.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\circularqueue.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicehelper.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\callbackdevice.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\localtimestampdirectoryvisitor.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\blueprintsobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\buildobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\coreobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\frameworkobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\mobileobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\networkingobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\onlineobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\platformobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\sequencerobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\vrobjectversion.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceconsole.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monitoredprocess.h", "e:\\epic games\\unreal engine\\ue5 projects\\unrealmcp_test1\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpcommonutils.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\leveleditorviewport.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\assetselection.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\camera\\cameracomponent.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\cameracomponent.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\public\\imageutils.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\public\\highresscreenshot.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\selection.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\selection.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\typedelementframework\\public\\elements\\framework\\typedelementlist.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\typedelementframework\\public\\elements\\framework\\typedelementcounter.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\typedelementframework\\uht\\typedelementcounter.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\typedelementruntime\\public\\elements\\framework\\typedelementselectionset.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\typedelementruntime\\public\\elements\\framework\\typedelementinterfacecustomization.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\typedelementruntime\\public\\elements\\framework\\typedelementlistobjectutil.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\typedelementruntime\\public\\elements\\interfaces\\typedelementselectioninterface.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\typedelementframework\\public\\elements\\framework\\typedelementlistproxy.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\typedelementframework\\uht\\typedelementlistproxy.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\typedelementruntime\\uht\\typedelementselectioninterface.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\typedelementruntime\\uht\\typedelementselectionset.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\selection.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\dialoguetypes.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystatics.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\staticmeshactor.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\staticmeshactor.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\directionallight.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\light.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\light.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\directionallight.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\pointlight.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\pointlight.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\spotlight.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\spotlight.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\camera\\cameraactor.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\cameraactor.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\lightcomponent.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\lightcomponentbase.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\lightcomponentbase.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\lightcomponent.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\directionallightcomponent.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\directionallightcomponent.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\pointlightcomponent.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\locallightcomponent.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\locallightcomponent.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\pointlightcomponent.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\spotlightcomponent.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\spotlightcomponent.generated.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\subsystems\\editoractorsubsystem.h", "e:\\epic games\\unreal engine\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\editoractorsubsystem.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}