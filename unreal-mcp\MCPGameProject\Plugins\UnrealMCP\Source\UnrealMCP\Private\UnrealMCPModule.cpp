#include "UnrealMCPModule.h"
#include "UnrealMCPBridge.h"
#include "Modules/ModuleManager.h"
#include "Styling/SlateStyleRegistry.h"
#include "Widgets/Layout/SGridPanel.h"
#include "Widgets/Layout/SUniformGridPanel.h"
#include "Widgets/Layout/SScrollBox.h"
#include "Widgets/Input/SEditableTextBox.h"
#include "Widgets/Text/STextBlock.h"
#include "ISettingsModule.h"
#include "EditorSubsystem.h"
#include "Editor.h"
#include "HttpModule.h"
#include "Interfaces/IHttpRequest.h"
#include "Interfaces/IHttpResponse.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

#define LOCTEXT_NAMESPACE "FUnrealMCPModule"



// Define a style set for our plugin
class FMCPPluginStyle : public FSlateStyleSet
{
public:
	FMCPPluginStyle() : FSlateStyleSet("MCPPluginStyle")
	{
		const FVector2D Icon16x16(16.0f, 16.0f);
		const FVector2D StatusSize(6.0f, 6.0f);

		// Use path constants instead of finding the plugin each time
		//SetContentRoot(MCPConstants::PluginResourcesPath);

		// Register icon
		FSlateImageBrush* MCPIconBrush = new FSlateImageBrush(
			RootToContentDir(TEXT("Icon128.png")),
			Icon16x16,
			FLinearColor::White,  // Tint (white preserves original colors)
			ESlateBrushTileType::NoTile  // Ensure no tiling, just the image
		);
		Set("MCPPlugin.ServerIcon", MCPIconBrush);

		// Create status indicator brushes
		const FLinearColor RunningColor(0.0f, 0.8f, 0.0f);  // Green
		const FLinearColor StoppedColor(0.8f, 0.0f, 0.0f);  // Red

		Set("MCPPlugin.StatusRunning", new FSlateRoundedBoxBrush(RunningColor, 3.0f, FVector2f(StatusSize)));
		Set("MCPPlugin.StatusStopped", new FSlateRoundedBoxBrush(StoppedColor, 3.0f, FVector2f(StatusSize)));

		// Define a custom button style with hover feedback
		FButtonStyle ToolbarButtonStyle = FAppStyle::Get().GetWidgetStyle<FButtonStyle>("LevelEditor.ToolBar.Button");

		// Normal state: fully transparent background
		ToolbarButtonStyle.SetNormal(FSlateColorBrush(FLinearColor(0, 0, 0, 0))); // Transparent

		// Hovered state: subtle overlay (e.g., light gray with low opacity)
		ToolbarButtonStyle.SetHovered(FSlateColorBrush(FLinearColor(0.2f, 0.2f, 0.2f, 0.3f))); // Semi-transparent gray

		// Pressed state: slightly darker overlay
		ToolbarButtonStyle.SetPressed(FSlateColorBrush(FLinearColor(0.1f, 0.1f, 0.1f, 0.5f))); // Darker semi-transparent gray

		// Register the custom style
		Set("MCPPlugin.TransparentToolbarButton", ToolbarButtonStyle);
	}

	static void Initialize()
	{
		if (!Instance.IsValid())
		{
			Instance = MakeShareable(new FMCPPluginStyle());
		}
	}

	static void Shutdown()
	{
		if (Instance.IsValid())
		{
			FSlateStyleRegistry::UnRegisterSlateStyle(*Instance);
			Instance.Reset();
		}
	}

	static TSharedPtr<FMCPPluginStyle> Get()
	{
		return Instance;
	}

private:
	static TSharedPtr<FMCPPluginStyle> Instance;
};

TSharedPtr<FMCPPluginStyle> FMCPPluginStyle::Instance = nullptr;



void FUnrealMCPModule::StartupModule()
{
	// Comment for deployment
	// Register style set
	// FMCPPluginStyle::Initialize();
	//FSlateStyleRegistry::RegisterSlateStyle(*FMCPPluginStyle::Get());

	// Register for post engine init to add toolbar button
	// First, make sure we're not already registered
	FCoreDelegates::OnPostEngineInit.RemoveAll(this);

	// Register settings
	if (ISettingsModule* SettingsModule = FModuleManager::GetModulePtr<ISettingsModule>("Settings"))
	{
		/*
		SettingsModule->RegisterSettings("Editor", "Plugins", "MCP Settings",
			LOCTEXT("MCPSettingsName", "MCP Settings"),
			LOCTEXT("MCPSettingsDescription", "Configure the MCP plugin settings"),
			GetMutableDefault<UMCPSettings>()
		);
		*/
	}

	UE_LOG(LogTemp, Display, TEXT("Registering OnPostEngineInit delegate"));
	// Comment for deployment
	// FCoreDelegates::OnPostEngineInit.AddRaw(this, &FUnrealMCPModule::ExtendLevelEditorToolbar);

	UE_LOG(LogTemp, Display, TEXT("Friday - Unreal MCP Module has started"));
}

void FUnrealMCPModule::ShutdownModule()
{
	// Unregister style set
	FMCPPluginStyle::Shutdown();

	// Unregister settings
	if (ISettingsModule* SettingsModule = FModuleManager::GetModulePtr<ISettingsModule>("Settings"))
	{
		SettingsModule->UnregisterSettings("Editor", "Plugins", "MCP Settings");
	}

	// Close control panel if open
	CloseMCPControlPanel();

	// Clean up delegates
	FCoreDelegates::OnPostEngineInit.RemoveAll(this);

	UE_LOG(LogTemp, Display, TEXT("Unreal MCP Module has shut down"));
}


void FUnrealMCPModule::ExtendLevelEditorToolbar()
{
	static bool bToolbarExtended = false;

	if (bToolbarExtended)
	{
		UE_LOG(LogTemp, Warning, TEXT("ExtendLevelEditorToolbar called but toolbar already extended, skipping"));
		return;
	}

	UE_LOG(LogTemp, Display, TEXT("ExtendLevelEditorToolbar called - first time"));

	UToolMenus::Get()->RegisterMenu("LevelEditor.MainMenu", "MainFrame.MainMenu");

	UToolMenu* ToolbarMenu = UToolMenus::Get()->ExtendMenu("LevelEditor.LevelEditorToolBar.User");
	if (ToolbarMenu)
	{
		FToolMenuSection& Section = ToolbarMenu->FindOrAddSection("MCP");

		// Add a custom widget instead of a static toolbar button
		Section.AddEntry(FToolMenuEntry::InitWidget(
			"MCPServerControl",
			SNew(SButton)
			.ButtonStyle(FMCPPluginStyle::Get().ToSharedRef(), "MCPPlugin.TransparentToolbarButton")
			//.ButtonStyle(FAppStyle::Get(), "LevelEditor.ToolBar.Button") // Match toolbar style
			.OnClicked(FOnClicked::CreateRaw(this, &FUnrealMCPModule::OpenMCPControlPanel_OnClicked))
			.ToolTipText(LOCTEXT("MCPButtonTooltip", "Open MCP AI Assistant"))
			.Content()
			[
				SNew(SOverlay)
					+ SOverlay::Slot()
					[
						SNew(SImage)
							.Image(FMCPPluginStyle::Get()->GetBrush("MCPPlugin.ServerIcon"))
							.ColorAndOpacity(FLinearColor::White)  // Ensure no tint overrides transparency
					]
					+ SOverlay::Slot()
					.HAlign(HAlign_Right)
					.VAlign(VAlign_Bottom)
					[
						SNew(SImage)
							.Image_Lambda([this]() -> const FSlateBrush* {
							return FMCPPluginStyle::Get()->GetBrush("MCPPlugin.StatusRunning");
								})
					]
			],
			FText::GetEmpty(),  // No label needed since the icon is visual
			true,   // bNoIndent
			false,  // bSearchable
			false
		));

		UE_LOG(LogTemp, Display, TEXT("MCP Server button added to main toolbar with dynamic icon"));
	}

	// Window menu code remains unchanged
	UToolMenu* WindowMenu = UToolMenus::Get()->ExtendMenu("LevelEditor.MainMenu.Window");
	if (WindowMenu)
	{
		FToolMenuSection& Section = WindowMenu->FindOrAddSection("WindowLayout");
		Section.AddMenuEntry(
			"MCPServerControlWindow",
			LOCTEXT("MCPWindowMenuLabel", "MCP AI Assistant"),
			LOCTEXT("MCPWindowMenuTooltip", "Open MCP AI Assistant"),
			FSlateIcon(FMCPPluginStyle::Get()->GetStyleSetName(), "MCPPlugin.ServerIcon"),
			FUIAction(
				FExecuteAction::CreateRaw(this, &FUnrealMCPModule::OpenMCPControlPanel),
				FCanExecuteAction()
			)
		);

		/*
		const FName MCPServerEditorTabId("MCPServerEditor");

		FGlobalTabmanager::Get()->RegisterTabSpawner(MCPServerEditorTabId,
			FOnSpawnTab::CreateRaw(this, &FUnrealMCPModule::SpawnMCPControlPanel))
			//.SetGroup(MediaBrowserGroup)
			.SetDisplayName(LOCTEXT("ImgMediaBandwidthMonitorTabTitle", "Bandwidth Monitor"))
			.SetTooltipText(LOCTEXT("ImgMediaBandwidthMonitorTooltipText", "Open the bandwidth monitor tab."))
			.SetIcon(FSlateIcon(FAppStyle::GetAppStyleSetName(), "SequenceRecorder.TabIcon"));
		*/

		UE_LOG(LogTemp, Display, TEXT("MCP Server entry added to Window menu"));
	}

	bToolbarExtended = true;
}


 void FUnrealMCPModule::OpenMCPControlPanel()
{
	
	// Create a new window
	 /*
	 TSharedRef<SDockTab> newTab = SNew(SDockTab)
		.Label(NSLOCTEXT("MCPControlPanel", "MCPControlPanelTitle", "MCP Server Control Panel"))
		[
			CreateMCPControlPanelContent()
		];
	*/

	// If the window already exists, just focus it
	 if (MCPControlPanelWindow.IsValid())
	 {
		 MCPControlPanelWindow->BringToFront();
		 return;
	 }

	 MCPControlPanelWindow = SNew(SWindow)
		 .Title(LOCTEXT("MCPChatbotTitle", "MCP AI Assistant"))
		 .SizingRule(ESizingRule::UserSized)
		 .SupportsMaximize(true)
		 .SupportsMinimize(true)
		 .HasCloseButton(true)
		 .CreateTitleBar(true)
		 .IsTopmostWindow(false)
		 .ClientSize(FVector2D(500, 400))
		 .MinWidth(400)
		 .MinHeight(300);

	 // Set the content of the window
	 MCPControlPanelWindow->SetContent(CreateMCPControlPanelContent());

	// Register a callback for when the window is closed
	MCPControlPanelWindow->GetOnWindowClosedEvent().AddRaw(this, &FUnrealMCPModule::OnMCPControlPanelClosed);

	// Show the window
	FSlateApplication::Get().AddWindow(MCPControlPanelWindow.ToSharedRef());

	// Add welcome message
	AddMessageToChat(TEXT("Hello! I'm your MCP AI Assistant. I'm here to help you with Unreal Engine development. Feel free to ask me anything!"), false);

	UE_LOG(LogTemp, Display, TEXT("MCP AI Assistant opened"));
}

FReply FUnrealMCPModule::OpenMCPControlPanel_OnClicked()
{
	OpenMCPControlPanel();

	return FReply::Handled();
}

void FUnrealMCPModule::OnMCPControlPanelClosed(const TSharedRef<SWindow>& Window)
{
	MCPControlPanelWindow.Reset();
	
	// Reset session state when closing
	bSessionCreated = false;
	CurrentSessionId.Empty();
	
	UE_LOG(LogTemp, Display, TEXT("MCP AI Assistant closed"));
}

void FUnrealMCPModule::CloseMCPControlPanel()
{
	if (MCPControlPanelWindow.IsValid())
	{
		MCPControlPanelWindow->RequestDestroyWindow();
		MCPControlPanelWindow.Reset();
		UE_LOG(LogTemp, Display, TEXT("MCP AI Assistant closed"));
	}
}

TSharedRef<SWidget> FUnrealMCPModule::CreateMCPControlPanelContent()
{
	return SNew(SBorder)
		.BorderImage(FAppStyle::GetBrush("ToolPanel.GroupBorder"))
		.BorderBackgroundColor(FLinearColor(0.08f, 0.08f, 0.08f, 1.0f)) // Very dark gray background
		.Padding(8.0f)
		[
			SNew(SVerticalBox)

			// Chat messages area
			+ SVerticalBox::Slot()
			.FillHeight(1.0f)
			.Padding(0, 0, 0, 8)
			[
				SNew(SBorder)
				.BorderImage(FAppStyle::GetBrush("DetailsView.CategoryMiddle"))
				.BorderBackgroundColor(FLinearColor(0.12f, 0.12f, 0.12f, 1.0f)) // Dark gray chat area
				.Padding(8.0f)
				[
					SAssignNew(ChatMessagesScrollBox, SScrollBox)
					.Orientation(Orient_Vertical)
					.ScrollBarAlwaysVisible(true)
					.ScrollBarVisibility(EVisibility::Visible)
					.ConsumeMouseWheel(EConsumeMouseWheel::WhenScrollingPossible)
					.AllowOverscroll(EAllowOverscroll::No)
					.AnimateWheelScrolling(true)
				]
			]

			// Input area
			+ SVerticalBox::Slot()
			.AutoHeight()
			.Padding(0, 4, 0, 0)
			[
				SNew(SBorder)
				.BorderImage(FAppStyle::GetBrush("DetailsView.CategoryMiddle"))
				.BorderBackgroundColor(FLinearColor(0.08f, 0.08f, 0.08f, 1.0f)) // Dark input area background
				.Padding(8.0f)
				[
					SNew(SHorizontalBox)

					// Text input field
					+ SHorizontalBox::Slot()
					.FillWidth(1.0f)
					.VAlign(VAlign_Center)
					.Padding(0, 0, 8, 0)
					[
						SAssignNew(ChatInputTextBox, SEditableTextBox)
						.HintText(LOCTEXT("ChatInputHint", "Type your message here..."))
						.OnTextCommitted(FOnTextCommitted::CreateRaw(this, &FUnrealMCPModule::OnChatInputCommitted))
						.BackgroundColor(FLinearColor(0.18f, 0.18f, 0.18f, 1.0f)) // Dark input field
						.ForegroundColor(FLinearColor(0.9f, 0.9f, 0.9f, 1.0f)) // Light text
					]

					// Send button
					+ SHorizontalBox::Slot()
					.AutoWidth()
					.VAlign(VAlign_Center)
					[
						SNew(SButton)
						.Text(LOCTEXT("SendButton", "Send"))
						.OnClicked(FOnClicked::CreateRaw(this, &FUnrealMCPModule::OnSendMessageClicked))
						.IsEnabled_Lambda([this]() -> bool {
							return ChatInputTextBox.IsValid() && !ChatInputTextBox->GetText().IsEmpty();
						})
						.ButtonColorAndOpacity(FLinearColor(0.2f, 0.4f, 0.8f, 1.0f)) // Blue send button
						.ContentPadding(FMargin(16, 6))
					]
				]
			]
		];
}



void FUnrealMCPModule::OnChatInputCommitted(const FText& Text, ETextCommit::Type CommitType)
{
	if (CommitType == ETextCommit::OnEnter)
	{
		OnSendMessageClicked();
	}
}

FReply FUnrealMCPModule::OnSendMessageClicked()
{
	if (!ChatInputTextBox.IsValid())
	{
		return FReply::Handled();
	}

	FString UserMessage = ChatInputTextBox->GetText().ToString().TrimStartAndEnd();
	if (UserMessage.IsEmpty())
	{
		return FReply::Handled();
	}

	// Add user message to chat
	AddMessageToChat(UserMessage, true);

	// Clear input field
	ChatInputTextBox->SetText(FText::GetEmpty());

	// Process the message (simulate AI response)
	ProcessUserMessage(UserMessage);

	return FReply::Handled();
}

void FUnrealMCPModule::AddMessageToChat(const FString& Message, bool bIsUserMessage)
{
	if (!ChatMessagesScrollBox.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("ChatMessagesScrollBox is not valid!"));
		return;
	}

	// Log message addition for debugging
	UE_LOG(LogTemp, Log, TEXT("Adding message to chat: %s (User: %s)"), *Message, bIsUserMessage ? TEXT("true") : TEXT("false"));

	// Create message bubble with modern styling
	FLinearColor MessageBubbleColor = bIsUserMessage ? 
		FLinearColor(0.2f, 0.4f, 0.8f, 1.0f) :      // Blue for user messages
		FLinearColor(0.25f, 0.25f, 0.25f, 1.0f);    // Dark gray for AI messages
	
	FLinearColor MessageTextColor = FLinearColor(0.95f, 0.95f, 0.95f, 1.0f); // White text for both
	FString MessagePrefix = bIsUserMessage ? TEXT("You: ") : TEXT("AI: ");

	// Create message container with proper alignment
	TSharedRef<SWidget> MessageContainer = SNew(SHorizontalBox)
		
		// Add spacer for user messages (right align)
		+ SHorizontalBox::Slot()
		.FillWidth(bIsUserMessage ? 0.3f : 0.0f)
		[
			SNew(SSpacer)
		]

		// Message bubble
		+ SHorizontalBox::Slot()
		.FillWidth(bIsUserMessage ? 0.7f : 0.8f)
		.Padding(bIsUserMessage ? FMargin(20, 0, 0, 0) : FMargin(0, 0, 20, 0))
		[
			SNew(SBorder)
			.BorderImage(FAppStyle::GetBrush("DetailsView.CategoryMiddle"))
			.BorderBackgroundColor(MessageBubbleColor)
			.Padding(FMargin(12.0f, 8.0f))
			[
				SNew(SVerticalBox)
				
				// Message prefix (smaller, dimmed)
				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(0, 0, 0, 2)
				[
					SNew(STextBlock)
					.Text(FText::FromString(MessagePrefix.TrimEnd()))
					.ColorAndOpacity(FLinearColor(0.7f, 0.7f, 0.7f, 1.0f))
					.Font(FCoreStyle::GetDefaultFontStyle("Regular", 9))
				]

				// Main message text
				+ SVerticalBox::Slot()
				.AutoHeight()
				[
					SNew(STextBlock)
					.Text(FText::FromString(Message))
					.ColorAndOpacity(MessageTextColor)
					.Font(FCoreStyle::GetDefaultFontStyle("Regular", 11))
					.AutoWrapText(true)
				]
			]
		]

		// Add spacer for AI messages (left align)
		+ SHorizontalBox::Slot()
		.FillWidth(bIsUserMessage ? 0.0f : 0.2f)
		[
			SNew(SSpacer)
		];

	// Add to scroll box with spacing
	ChatMessagesScrollBox->AddSlot()
		.Padding(FMargin(0, 4))
		[
			MessageContainer
		];

	// Ensure the scroll box updates and scrolls to bottom
	ChatMessagesScrollBox->Invalidate(EInvalidateWidget::Layout);
	ChatMessagesScrollBox->ScrollToEnd();
	
	// Log current message count for debugging
	UE_LOG(LogTemp, Log, TEXT("Total messages in chat: %d"), ChatMessagesScrollBox->GetChildren()->Num());
}

void FUnrealMCPModule::ProcessUserMessage(const FString& Message)
{
	// Check if we have a session, if not create one first
	if (!bSessionCreated || CurrentSessionId.IsEmpty())
	{
		CreateSessionThenSendMessage(Message);
	}
	else
	{
		SendMessageToAgent(Message);
	}
}

void FUnrealMCPModule::CreateSessionThenSendMessage(const FString& Message)
{
	// Create HTTP request for session creation
	TSharedRef<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = FHttpModule::Get().CreateRequest();
	
	// Generate a unique session ID
	CurrentSessionId = FString::Printf(TEXT("unreal_session_%d"), FDateTime::Now().GetTicks());
	
	// Create session endpoint: /apps/{app_name}/users/{user_id}/sessions/{session_id}
	FString SessionEndpoint = FString::Printf(TEXT("/apps/UnrealAI-agent/users/unreal_user/sessions/%s"), *CurrentSessionId);
	HttpRequest->SetURL(AgentServerURL + SessionEndpoint);
	HttpRequest->SetVerb(TEXT("POST"));
	HttpRequest->SetHeader(TEXT("Content-Type"), TEXT("application/json"));
	
	// Create session with empty state
	TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
	TSharedPtr<FJsonObject> StateObject = MakeShareable(new FJsonObject);
	JsonObject->SetObjectField(TEXT("state"), StateObject);
	
	// Convert JSON to string
	FString OutputString;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
	FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);
	
	UE_LOG(LogTemp, Log, TEXT("Creating session at: %s"), *(AgentServerURL + SessionEndpoint));
	UE_LOG(LogTemp, Log, TEXT("Session creation payload: %s"), *OutputString);
	
	// Set request content
	HttpRequest->SetContentAsString(OutputString);
	
	// Bind response callback with the pending message
	HttpRequest->OnProcessRequestComplete().BindLambda([this, Message](FHttpRequestPtr Req, FHttpResponsePtr Resp, bool bSuccess) {
		OnSessionCreated(Req, Resp, bSuccess, Message);
	});
	
	// Send the request
	if (!HttpRequest->ProcessRequest())
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to send session creation request"));
		AddMessageToChat(TEXT("Sorry, I couldn't create a session with the AI agent."), false);
	}
	else
	{
		UE_LOG(LogTemp, Log, TEXT("Creating session for message: %s"), *Message);
		AddMessageToChat(TEXT("Initializing AI session..."), false);
	}
}

void FUnrealMCPModule::OnSessionCreated(TSharedPtr<IHttpRequest, ESPMode::ThreadSafe> Request, TSharedPtr<IHttpResponse, ESPMode::ThreadSafe> Response, bool bWasSuccessful, FString PendingMessage)
{
	if (!bWasSuccessful || !Response.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("Session creation failed"));
		AddMessageToChat(TEXT("Failed to create AI session. Please try again."), false);
		return;
	}

	int32 ResponseCode = Response->GetResponseCode();
	FString ResponseContent = Response->GetContentAsString();
	
	UE_LOG(LogTemp, Log, TEXT("Session creation response code: %d"), ResponseCode);
	UE_LOG(LogTemp, Log, TEXT("Session creation response: %s"), *ResponseContent);

	if (ResponseCode == 200)
	{
		bSessionCreated = true;
		UE_LOG(LogTemp, Log, TEXT("Session created successfully. Now sending message: %s"), *PendingMessage);
		
		// Now send the actual message
		SendMessageToAgent(PendingMessage);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Session creation returned error code: %d"), ResponseCode);
		AddMessageToChat(FString::Printf(TEXT("Failed to create session (code %d). Please try again."), ResponseCode), false);
	}
}

void FUnrealMCPModule::SendMessageToAgent(const FString& Message)
{
	// Create HTTP request
	TSharedRef<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = FHttpModule::Get().CreateRequest();
	
	// Use the correct /run endpoint as per ADK documentation
	HttpRequest->SetURL(AgentServerURL + TEXT("/run"));
	HttpRequest->SetVerb(TEXT("POST"));
	HttpRequest->SetHeader(TEXT("Content-Type"), TEXT("application/json"));
	
	// Create JSON payload with the correct ADK format
	TSharedPtr<FJsonObject> JsonObject = MakeShareable(new FJsonObject);
	JsonObject->SetStringField(TEXT("appName"), TEXT("UnrealAI-agent"));
	JsonObject->SetStringField(TEXT("userId"), TEXT("unreal_user"));
	JsonObject->SetStringField(TEXT("sessionId"), CurrentSessionId);
	
	// Create the newMessage object with the correct structure
	TSharedPtr<FJsonObject> NewMessage = MakeShareable(new FJsonObject);
	NewMessage->SetStringField(TEXT("role"), TEXT("user"));
	
	// Create parts array with text content
	TArray<TSharedPtr<FJsonValue>> PartsArray;
	TSharedPtr<FJsonObject> TextPart = MakeShareable(new FJsonObject);
	TextPart->SetStringField(TEXT("text"), Message);
	PartsArray.Add(MakeShareable(new FJsonValueObject(TextPart)));
	
	NewMessage->SetArrayField(TEXT("parts"), PartsArray);
	JsonObject->SetObjectField(TEXT("newMessage"), NewMessage);
	
	// Convert JSON to string
	FString OutputString;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
	FJsonSerializer::Serialize(JsonObject.ToSharedRef(), Writer);
	
	UE_LOG(LogTemp, Log, TEXT("Sending request to: %s"), *(AgentServerURL + TEXT("/run")));
	UE_LOG(LogTemp, Log, TEXT("Request payload: %s"), *OutputString);
	
	// Set request content
	HttpRequest->SetContentAsString(OutputString);
	
	// Bind response callback
	HttpRequest->OnProcessRequestComplete().BindRaw(this, &FUnrealMCPModule::OnAgentResponseReceived);
	
	// Send the request
	if (!HttpRequest->ProcessRequest())
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to send HTTP request to UnrealAI agent"));
		AddMessageToChat(TEXT("Sorry, I'm having trouble connecting to the AI agent. Please make sure the agent server is running."), false);
	}
	else
	{
		UE_LOG(LogTemp, Log, TEXT("Sent message to UnrealAI agent: %s"), *Message);
		// Show a "thinking" indicator
		AddMessageToChat(TEXT("Thinking..."), false);
	}
}

void FUnrealMCPModule::OnAgentResponseReceived(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful)
{
	if (!bWasSuccessful || !Response.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("HTTP request failed or invalid response"));
		AddMessageToChat(TEXT("Sorry, I couldn't get a response from the AI agent. Please check if the agent server is running."), false);
		return;
	}

	int32 ResponseCode = Response->GetResponseCode();
	FString ResponseContent = Response->GetContentAsString();
	
	UE_LOG(LogTemp, Log, TEXT("Agent response code: %d"), ResponseCode);
	UE_LOG(LogTemp, Log, TEXT("Agent response content: %s"), *ResponseContent);

	if (ResponseCode == 200)
	{
		// Parse JSON response from ADK /run endpoint
		// The response should be an array of events
		TArray<TSharedPtr<FJsonValue>> EventsArray;
		TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(ResponseContent);
		
		if (FJsonSerializer::Deserialize(Reader, EventsArray))
		{
			FString AgentMessage;
			
			// Process events to extract the final agent response
			for (const auto& EventValue : EventsArray)
			{
				const TSharedPtr<FJsonObject>* EventObjPtr;
				if (EventValue->TryGetObject(EventObjPtr) && EventObjPtr && EventObjPtr->IsValid())
				{
					TSharedPtr<FJsonObject> EventObj = *EventObjPtr;
					// Look for content with role "model" (agent response)
					if (EventObj->HasField(TEXT("content")))
					{
						const TSharedPtr<FJsonObject>* ContentObj;
						if (EventObj->TryGetObjectField(TEXT("content"), ContentObj))
						{
							FString Role;
							if ((*ContentObj)->TryGetStringField(TEXT("role"), Role) && Role == TEXT("model"))
							{
								// Extract text from parts array
								const TArray<TSharedPtr<FJsonValue>>* PartsArray;
								if ((*ContentObj)->TryGetArrayField(TEXT("parts"), PartsArray))
								{
									for (const auto& PartValue : *PartsArray)
									{
										const TSharedPtr<FJsonObject>* PartObjPtr;
										if (PartValue->TryGetObject(PartObjPtr) && PartObjPtr && PartObjPtr->IsValid())
										{
											TSharedPtr<FJsonObject> PartObj = *PartObjPtr;
											FString Text;
											if (PartObj->TryGetStringField(TEXT("text"), Text))
											{
												AgentMessage += Text;
											}
										}
									}
								}
							}
						}
					}
				}
			}
			
			// If we still don't have a message, try to extract any text content
			if (AgentMessage.IsEmpty())
			{
				for (const auto& EventValue : EventsArray)
				{
					const TSharedPtr<FJsonObject>* EventObjPtr;
					if (EventValue->TryGetObject(EventObjPtr) && EventObjPtr && EventObjPtr->IsValid())
					{
						TSharedPtr<FJsonObject> EventObj = *EventObjPtr;
						// Look for any text content in the events
						if (EventObj->HasField(TEXT("content")))
						{
							const TSharedPtr<FJsonObject>* ContentObj;
							if (EventObj->TryGetObjectField(TEXT("content"), ContentObj))
							{
								const TArray<TSharedPtr<FJsonValue>>* PartsArray;
								if ((*ContentObj)->TryGetArrayField(TEXT("parts"), PartsArray))
								{
									for (const auto& PartValue : *PartsArray)
									{
										const TSharedPtr<FJsonObject>* PartObjPtr;
										if (PartValue->TryGetObject(PartObjPtr) && PartObjPtr && PartObjPtr->IsValid())
										{
											TSharedPtr<FJsonObject> PartObj = *PartObjPtr;
											FString Text;
											if (PartObj->TryGetStringField(TEXT("text"), Text))
											{
												AgentMessage += Text;
											}
										}
									}
								}
							}
						}
					}
				}
			}
			
			// If we still don't have a message, show debug info
			if (AgentMessage.IsEmpty())
			{
				AgentMessage = FString::Printf(TEXT("Received %d events but couldn't extract text response. Check logs for details."), EventsArray.Num());
				UE_LOG(LogTemp, Warning, TEXT("No text response found in %d events"), EventsArray.Num());
			}
			
			// Remove only the last "Thinking..." message if it exists
			if (ChatMessagesScrollBox.IsValid() && ChatMessagesScrollBox->GetChildren()->Num() > 0)
			{
				// Remove only the last message (which should be "Thinking...")
				int32 LastIndex = ChatMessagesScrollBox->GetChildren()->Num() - 1;
				if (LastIndex >= 0)
				{
					TSharedPtr<SWidget> LastWidget = ChatMessagesScrollBox->GetChildren()->GetChildAt(LastIndex);
					if (LastWidget.IsValid())
					{
						ChatMessagesScrollBox->RemoveSlot(LastWidget.ToSharedRef());
						UE_LOG(LogTemp, Log, TEXT("Removed thinking message. Remaining messages: %d"), ChatMessagesScrollBox->GetChildren()->Num());
					}
				}
			}
			
			AddMessageToChat(AgentMessage, false);
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to parse JSON response from agent"));
			AddMessageToChat(TEXT("I received a response but couldn't understand it. Please try again."), false);
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Agent server returned error code: %d"), ResponseCode);
		AddMessageToChat(FString::Printf(TEXT("The AI agent returned an error (code %d). Please try again."), ResponseCode), false);
	}
}

#undef LOCTEXT_NAMESPACE
	
IMPLEMENT_MODULE(FUnrealMCPModule, UnrealMCP) 