{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "UnrealMCP", "Description": "Friday - Model Context Protocol implementation for Unreal Engine", "Category": "Editor", "CreatedBy": "Friday", "CreatedByURL": "", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "https://discord.gg/PfTJsYw7", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": false, "Installed": false, "Modules": [{"Name": "UnrealMCP", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"]}], "Plugins": [{"Name": "EditorScriptingUtilities", "Enabled": true}, {"Name": "EnhancedInput", "Enabled": true}]}