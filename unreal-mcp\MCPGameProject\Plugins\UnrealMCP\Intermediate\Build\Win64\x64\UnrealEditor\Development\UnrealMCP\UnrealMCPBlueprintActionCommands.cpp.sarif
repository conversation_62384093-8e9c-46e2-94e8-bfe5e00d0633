{"version": "2.1.0", "$schema": "https://schemastore.azurewebsites.net/schemas/json/sarif-2.1.0-rtm.5.json", "runs": [{"results": [{"ruleId": "C4457", "message": {"text": "declaration of 'PinType' hides function parameter"}, "analysisTarget": {"uri": "file:///E:/Epic Games/Unreal Engine/UE5 projects/unrealmcp_test1/Plugins/UnrealMCP/Source/UnrealMCP/Private/Commands/UnrealMCPBlueprintActionCommands.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///E:/Epic Games/Unreal Engine/UE5 projects/unrealmcp_test1/Plugins/UnrealMCP/Source/UnrealMCP/Private/Commands/UnrealMCPBlueprintActionCommands.cpp"}, "region": {"startLine": 523, "startColumn": 21, "snippet": {"text": "            FString PinType = Property->GetCPPType();"}}}}], "relatedLocations": [{"id": 0, "physicalLocation": {"artifactLocation": {"uri": "file:///E:/Epic Games/Unreal Engine/UE5 projects/unrealmcp_test1/Plugins/UnrealMCP/Source/UnrealMCP/Private/Commands/UnrealMCPBlueprintActionCommands.cpp"}, "region": {"startLine": 254, "startColumn": 76, "snippet": {"text": "FString UUnrealMCPBlueprintActionCommands::GetActionsForPin(const FString& PinType, const FString& PinSubCategory, const FString& SearchFilter, int32 MaxResults)"}}}, "message": {"text": "see declaration of 'PinType'"}}]}], "tool": {"driver": {"name": "MSVC", "shortDescription": {"text": "Microsoft Visual C++ Compiler Warnings/Errors"}, "informationUri": "https://docs.microsoft.com/cpp/error-messages/compiler-errors-1/c-cpp-build-errors"}}}]}